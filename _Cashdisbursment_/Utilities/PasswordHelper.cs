using System.Security.Cryptography;
using System.Text;

namespace _Cashdisbursment_.Utilities
{
    public static class PasswordHelper
    {
        public static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ZimdefSalt2024"));
            return Convert.ToBase64String(hashedBytes);
        }

        public static void GenerateAdminCredentials()
        {
            string email = "<EMAIL>";
            string password = "Admin123!";
            string hashedPassword = HashPassword(password);
            
            Console.WriteLine("=== ADMIN CREDENTIALS ===");
            Console.WriteLine($"Email: {email}");
            Console.WriteLine($"Password: {password}");
            Console.WriteLine($"Hashed Password: {hashedPassword}");
            Console.WriteLine("=========================");
            Console.WriteLine();
            Console.WriteLine("SQL to create admin user:");
            Console.WriteLine($"INSERT INTO Users (Email, Password, Status, Role) VALUES ('{email}', '{hashedPassword}', 1, 'SuperAdmin')");
        }
    }
}
