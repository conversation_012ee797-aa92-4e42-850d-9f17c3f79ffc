using _Cashdisbursment_.Models;
using System.Text.Json;

namespace _Cashdisbursment_.Utilities
{
    public static class AuthUtility
    {
        private const string SessionKey = "CurrentUser";

        public static void SetCurrentUser(ISession session, User user)
        {
            var userJson = JsonSerializer.Serialize(new
            {
                user.UserID,
                user.Email,
                user.Role,
                user.CompanyID,
                CompanyName = user.Company?.Name
            });
            session.SetString(SessionKey, userJson);
        }

        public static User? GetCurrentUser(ISession session)
        {
            var userJson = session.GetString(SessionKey);
            if (string.IsNullOrEmpty(userJson))
                return null;

            try
            {
                var userData = JsonSerializer.Deserialize<JsonElement>(userJson);
                return new User
                {
                    UserID = userData.GetProperty("UserID").GetInt32(),
                    Email = userData.GetProperty("Email").GetString() ?? "",
                    Role = userData.GetProperty("Role").GetString() ?? "",
                    CompanyID = userData.TryGetProperty("CompanyID", out var companyIdProp) && 
                               companyIdProp.ValueKind != JsonValueKind.Null ? 
                               companyIdProp.GetInt32() : null,
                    Company = userData.TryGetProperty("CompanyName", out var companyNameProp) && 
                             companyNameProp.ValueKind != JsonValueKind.Null ?
                             new Company { Name = companyNameProp.GetString() ?? "" } : null
                };
            }
            catch
            {
                return null;
            }
        }

        public static void ClearCurrentUser(ISession session)
        {
            session.Remove(SessionKey);
        }

        public static bool IsAuthenticated(ISession session)
        {
            return GetCurrentUser(session) != null;
        }

        public static bool IsAdmin(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && (user.Role == "Admin" || user.Role == "SuperAdmin");
        }

        public static bool IsSuperAdmin(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && user.Role == "SuperAdmin";
        }

        public static bool IsCompanyUser(ISession session)
        {
            var user = GetCurrentUser(session);
            return user != null && user.Role == "Company";
        }

        public static bool HasCompanyAccess(ISession session, int companyId)
        {
            var user = GetCurrentUser(session);
            if (user == null) return false;

            // Admins can access all companies
            if (IsAdmin(session)) return true;

            // Company users can only access their own company
            return user.CompanyID == companyId;
        }
    }
}
