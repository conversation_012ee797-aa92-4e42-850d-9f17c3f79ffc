{"version": 2, "dgSpecHash": "qEPWjtLj2Mk=", "success": true, "projectFilePath": "/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/_Cashdisbursment_.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/bouncycastle.cryptography/2.4.0/bouncycastle.cryptography.2.4.0.nupkg.sha512", "/home/<USER>/.nuget/packages/mailkit/4.8.0/mailkit.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.platforms/3.1.0/microsoft.netcore.platforms.3.1.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.win32.registry/4.7.0/microsoft.win32.registry.4.7.0.nupkg.sha512", "/home/<USER>/.nuget/packages/mimekit/4.8.0/mimekit.4.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/runtime.native.system.data.sqlclient.sni/4.7.0/runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "/home/<USER>/.nuget/packages/runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/home/<USER>/.nuget/packages/runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/home/<USER>/.nuget/packages/runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.data.sqlclient/4.8.6/system.data.sqlclient.4.8.6.nupkg.sha512", "/home/<USER>/.nuget/packages/system.formats.asn1/8.0.1/system.formats.asn1.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.security.accesscontrol/4.7.0/system.security.accesscontrol.4.7.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.security.cryptography.pkcs/8.0.0/system.security.cryptography.pkcs.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.security.principal.windows/4.7.0/system.security.principal.windows.4.7.0.nupkg.sha512"], "logs": []}