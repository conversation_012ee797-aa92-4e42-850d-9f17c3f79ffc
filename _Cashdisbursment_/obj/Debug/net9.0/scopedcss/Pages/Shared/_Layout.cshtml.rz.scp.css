/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-ivjs8njzby] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-ivjs8njzby] {
  color: #0077cc;
}

.btn-primary[b-ivjs8njzby] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-ivjs8njzby], .nav-pills .show > .nav-link[b-ivjs8njzby] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-ivjs8njzby] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-ivjs8njzby] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-ivjs8njzby] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-ivjs8njzby] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-ivjs8njzby] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
