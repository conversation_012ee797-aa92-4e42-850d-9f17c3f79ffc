{"format": 1, "restore": {"/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/_Cashdisbursment_.csproj": {}}, "projects": {"/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/_Cashdisbursment_.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/_Cashdisbursment_.csproj", "projectName": "_Cashdisbursment_", "projectPath": "/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/_Cashdisbursment_.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/deeplabit/_Cashdisbursment_/_Cashdisbursment_/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/lib/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MailKit": {"target": "Package", "version": "[4.8.0, )"}, "MimeKit": {"target": "Package", "version": "[4.8.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/9.0.106/PortableRuntimeIdentifierGraph.json"}}}}}