using _Cashdisbursment_.Models;
using Microsoft.Data.SqlClient;

namespace _Cashdisbursment_.Services
{
    public class ApprovalService
    {
        private readonly DatabaseService _dbService;

        public ApprovalService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<List<ApprovalList>> GetAllApproversAsync()
        {
            const string query = @"
                SELECT Id, Email, ApproverNum
                FROM ApprovalList
                ORDER BY ApproverNum";

            var approvers = new List<ApprovalList>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                approvers.Add(new ApprovalList
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Email = reader.GetString(reader.GetOrdinal("Email")),
                    ApproverNum = reader.GetInt32(reader.GetOrdinal("ApproverNum"))
                });
            }

            return approvers;
        }

        public async Task<ApprovalList?> GetNextApproverAsync(int currentApprovalLevel)
        {
            const string query = @"
                SELECT Id, Email, ApproverNum
                FROM ApprovalList
                WHERE ApproverNum = @ApproverNum";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@ApproverNum", currentApprovalLevel + 1);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new ApprovalList
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Email = reader.GetString(reader.GetOrdinal("Email")),
                    ApproverNum = reader.GetInt32(reader.GetOrdinal("ApproverNum"))
                };
            }

            return null;
        }

        public async Task<int> GetMaxApprovalLevelAsync()
        {
            const string query = "SELECT ISNULL(MAX(ApproverNum), 0) FROM ApprovalList";
            return await _dbService.ExecuteScalarAsync<int>(query);
        }

        public async Task<bool> AddApproverAsync(ApprovalList approver)
        {
            const string query = @"
                INSERT INTO ApprovalList (Email, ApproverNum)
                VALUES (@Email, @ApproverNum)";

            var parameters = new[]
            {
                new SqlParameter("@Email", approver.Email),
                new SqlParameter("@ApproverNum", approver.ApproverNum)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> UpdateApproverAsync(ApprovalList approver)
        {
            const string query = @"
                UPDATE ApprovalList 
                SET Email = @Email, ApproverNum = @ApproverNum
                WHERE Id = @Id";

            var parameters = new[]
            {
                new SqlParameter("@Id", approver.Id),
                new SqlParameter("@Email", approver.Email),
                new SqlParameter("@ApproverNum", approver.ApproverNum)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> DeleteApproverAsync(int id)
        {
            const string query = "DELETE FROM ApprovalList WHERE Id = @Id";
            var parameters = new[] { new SqlParameter("@Id", id) };
            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            var query = "SELECT COUNT(*) FROM ApprovalList WHERE Email = @Email";
            var parameters = new List<SqlParameter> { new("@Email", email) };

            if (excludeId.HasValue)
            {
                query += " AND Id != @Id";
                parameters.Add(new SqlParameter("@Id", excludeId.Value));
            }

            var count = await _dbService.ExecuteScalarAsync<int>(query, parameters.ToArray());
            return count > 0;
        }

        public async Task<bool> ApproverNumExistsAsync(int approverNum, int? excludeId = null)
        {
            var query = "SELECT COUNT(*) FROM ApprovalList WHERE ApproverNum = @ApproverNum";
            var parameters = new List<SqlParameter> { new("@ApproverNum", approverNum) };

            if (excludeId.HasValue)
            {
                query += " AND Id != @Id";
                parameters.Add(new SqlParameter("@Id", excludeId.Value));
            }

            var count = await _dbService.ExecuteScalarAsync<int>(query, parameters.ToArray());
            return count > 0;
        }
    }
}
