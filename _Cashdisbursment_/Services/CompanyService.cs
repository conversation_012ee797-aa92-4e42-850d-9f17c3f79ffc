using _Cashdisbursment_.Models;
using System.Data.SqlClient;

namespace _Cashdisbursment_.Services
{
    public class CompanyService
    {
        private readonly DatabaseService _dbService;

        public CompanyService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<Company?> GetCompanyByIdAsync(int companyId)
        {
            const string query = @"
                SELECT CompanyID, Name, Email, Contact, Address, Status
                FROM Company
                WHERE CompanyID = @CompanyID";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@CompanyID", companyId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new Company
                {
                    CompanyID = reader.GetInt32("CompanyID"),
                    Name = reader.GetString("Name"),
                    Email = reader.GetString("Email"),
                    Contact = reader.GetString("Contact"),
                    Address = reader.GetString("Address"),
                    Status = reader.GetBoolean("Status")
                };
            }

            return null;
        }

        public async Task<Company?> GetCompanyByEmailAsync(string email)
        {
            const string query = @"
                SELECT CompanyID, Name, Email, Contact, Address, Status
                FROM Company
                WHERE Email = @Email";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@Email", email);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new Company
                {
                    CompanyID = reader.GetInt32("CompanyID"),
                    Name = reader.GetString("Name"),
                    Email = reader.GetString("Email"),
                    Contact = reader.GetString("Contact"),
                    Address = reader.GetString("Address"),
                    Status = reader.GetBoolean("Status")
                };
            }

            return null;
        }

        public async Task<List<Company>> GetAllCompaniesAsync()
        {
            const string query = @"
                SELECT CompanyID, Name, Email, Contact, Address, Status
                FROM Company
                ORDER BY Name";

            var companies = new List<Company>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                companies.Add(new Company
                {
                    CompanyID = reader.GetInt32("CompanyID"),
                    Name = reader.GetString("Name"),
                    Email = reader.GetString("Email"),
                    Contact = reader.GetString("Contact"),
                    Address = reader.GetString("Address"),
                    Status = reader.GetBoolean("Status")
                });
            }

            return companies;
        }

        public async Task<int> CreateCompanyAsync(Company company)
        {
            const string query = @"
                INSERT INTO Company (Name, Email, Contact, Address, Status)
                OUTPUT INSERTED.CompanyID
                VALUES (@Name, @Email, @Contact, @Address, @Status)";

            var parameters = new[]
            {
                new SqlParameter("@Name", company.Name),
                new SqlParameter("@Email", company.Email),
                new SqlParameter("@Contact", company.Contact),
                new SqlParameter("@Address", company.Address),
                new SqlParameter("@Status", company.Status)
            };

            var companyId = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return companyId;
        }

        public async Task<bool> UpdateCompanyAsync(Company company)
        {
            const string query = @"
                UPDATE Company 
                SET Name = @Name, Email = @Email, Contact = @Contact, 
                    Address = @Address, Status = @Status
                WHERE CompanyID = @CompanyID";

            var parameters = new[]
            {
                new SqlParameter("@CompanyID", company.CompanyID),
                new SqlParameter("@Name", company.Name),
                new SqlParameter("@Email", company.Email),
                new SqlParameter("@Contact", company.Contact),
                new SqlParameter("@Address", company.Address),
                new SqlParameter("@Status", company.Status)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeCompanyId = null)
        {
            var query = "SELECT COUNT(*) FROM Company WHERE Email = @Email";
            var parameters = new List<SqlParameter> { new("@Email", email) };

            if (excludeCompanyId.HasValue)
            {
                query += " AND CompanyID != @CompanyID";
                parameters.Add(new SqlParameter("@CompanyID", excludeCompanyId.Value));
            }

            var count = await _dbService.ExecuteScalarAsync<int>(query, parameters.ToArray());
            return count > 0;
        }

        public async Task<bool> ToggleCompanyStatusAsync(int companyId)
        {
            const string query = @"
                UPDATE Company 
                SET Status = CASE WHEN Status = 1 THEN 0 ELSE 1 END
                WHERE CompanyID = @CompanyID";

            var parameters = new[] { new SqlParameter("@CompanyID", companyId) };
            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }
    }
}
