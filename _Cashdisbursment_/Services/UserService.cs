using _Cashdisbursment_.Models;
using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace _Cashdisbursment_.Services
{
    public class UserService
    {
        private readonly DatabaseService _dbService;

        public UserService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            const string query = @"
                SELECT u.UserID, u.Email, u.Password, u.Status, u.CompanyID, u.Role,
                       c.Name as CompanyName
                FROM Users u
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID
                WHERE u.Email = @Email";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@Email", email);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new User
                {
                    UserID = reader.GetInt32(reader.GetOrdinal("UserID")),
                    Email = reader.GetString(reader.GetOrdinal("Email")),
                    Password = reader.GetString(reader.GetOrdinal("Password")),
                    Status = reader.GetBoolean(reader.GetOrdinal("Status")),
                    CompanyID = reader.IsDBNull(reader.GetOrdinal("CompanyID")) ? null : reader.GetInt32(reader.GetOrdinal("CompanyID")),
                    Role = reader.GetString(reader.GetOrdinal("Role")),
                    Company = reader.IsDBNull(reader.GetOrdinal("CompanyName")) ? null : new Models.Company
                    {
                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                        Name = reader.GetString(reader.GetOrdinal("CompanyName"))
                    }
                };
            }

            return null;
        }

        public async Task<User?> ValidateUserAsync(string email, string password)
        {
            var user = await GetUserByEmailAsync(email);
            if (user != null && user.Status && VerifyPassword(password, user.Password))
            {
                return user;
            }
            return null;
        }

        public async Task<bool> CreateUserAsync(User user)
        {
            const string query = @"
                INSERT INTO Users (Email, Password, Status, CompanyID, Role)
                VALUES (@Email, @Password, @Status, @CompanyID, @Role)";

            var hashedPassword = HashPassword(user.Password);

            var parameters = new[]
            {
                new SqlParameter("@Email", user.Email),
                new SqlParameter("@Password", hashedPassword),
                new SqlParameter("@Status", user.Status),
                new SqlParameter("@CompanyID", (object?)user.CompanyID ?? DBNull.Value),
                new SqlParameter("@Role", user.Role)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            const string query = @"
                UPDATE Users 
                SET Email = @Email, Status = @Status, CompanyID = @CompanyID, Role = @Role
                WHERE UserID = @UserID";

            var parameters = new[]
            {
                new SqlParameter("@UserID", user.UserID),
                new SqlParameter("@Email", user.Email),
                new SqlParameter("@Status", user.Status),
                new SqlParameter("@CompanyID", (object?)user.CompanyID ?? DBNull.Value),
                new SqlParameter("@Role", user.Role)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string newPassword)
        {
            const string query = "UPDATE Users SET Password = @Password WHERE UserID = @UserID";

            var hashedPassword = HashPassword(newPassword);
            var parameters = new[]
            {
                new SqlParameter("@UserID", userId),
                new SqlParameter("@Password", hashedPassword)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ZimdefSalt2024"));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            return HashPassword(password) == hashedPassword;
        }

        public string GenerateRandomPassword(int length = 8)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
