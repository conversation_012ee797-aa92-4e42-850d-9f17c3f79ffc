using MailKit.Net.Smtp;
using MimeKit;

namespace _Cashdisbursment_.Services
{
    public class EmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                var emailSettings = _configuration.GetSection("EmailSettings");
                var smtpServer = emailSettings["SmtpServer"];
                var senderEmail = emailSettings["SenderEmail"];
                var senderName = emailSettings["SenderName"];
                var password = emailSettings["Password"];

                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(senderName, senderEmail));
                message.To.Add(new MailboxAddress("", toEmail));
                message.Subject = subject;

                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                    bodyBuilder.HtmlBody = body;
                else
                    bodyBuilder.TextBody = body;

                message.Body = bodyBuilder.ToMessageBody();

                using var client = new SmtpClient();
                await client.ConnectAsync(smtpServer, 587, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(senderEmail, password);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                Console.WriteLine($"Email sending failed: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendCompanyRegistrationEmailAsync(string companyEmail, string companyName, 
            string userEmail, string password)
        {
            var subject = "Welcome to Zimdef Cash Disbursement System";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Welcome to Zimdef Cash Disbursement System</h2>
                    <p>Dear {companyName},</p>
                    <p>Your company has been successfully registered in our cash disbursement system.</p>
                    <p><strong>Login Credentials:</strong></p>
                    <ul>
                        <li>Email: {userEmail}</li>
                        <li>Password: {password}</li>
                    </ul>
                    <p>Please log in to the system and change your password immediately.</p>
                    <p>You can now submit fund applications through our portal.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendApplicationStatusEmailAsync(string companyEmail, string companyName, 
            int applicationId, string status, string? comment = null)
        {
            var subject = $"Application #{applicationId} Status Update";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Application Status Update</h2>
                    <p>Dear {companyName},</p>
                    <p>Your application #{applicationId} status has been updated to: <strong>{status}</strong></p>
                    {(string.IsNullOrEmpty(comment) ? "" : $"<p><strong>Comment:</strong> {comment}</p>")}
                    <p>Please log in to your portal to view more details.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendDisbursementNotificationAsync(string companyEmail, string companyName, 
            int applicationId, double amount, string disbursedBy)
        {
            var subject = $"Funds Disbursed - Application #{applicationId}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Funds Disbursed</h2>
                    <p>Dear {companyName},</p>
                    <p>We are pleased to inform you that funds have been disbursed for your application #{applicationId}.</p>
                    <p><strong>Amount Disbursed:</strong> ${amount:N2}</p>
                    <p><strong>Disbursed By:</strong> {disbursedBy}</p>
                    <p><strong>Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm}</p>
                    <p>Please ensure to submit your acquittal documentation as required.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement Team</p>
                </body>
                </html>";

            return await SendEmailAsync(companyEmail, subject, body);
        }

        public async Task<bool> SendApprovalRequestEmailAsync(string approverEmail, int applicationId, 
            string companyName, double amount, string description)
        {
            var subject = $"Approval Required - Application #{applicationId}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif;'>
                    <h2>Approval Required</h2>
                    <p>Dear Approver,</p>
                    <p>A new application requires your approval:</p>
                    <ul>
                        <li><strong>Application ID:</strong> #{applicationId}</li>
                        <li><strong>Company:</strong> {companyName}</li>
                        <li><strong>Amount:</strong> ${amount:N2}</li>
                        <li><strong>Description:</strong> {description}</li>
                    </ul>
                    <p>Please log in to the admin portal to review and approve/reject this application.</p>
                    <br>
                    <p>Best regards,<br>Zimdef Cash Disbursement System</p>
                </body>
                </html>";

            return await SendEmailAsync(approverEmail, subject, body);
        }
    }
}
