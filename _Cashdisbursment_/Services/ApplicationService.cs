using _Cashdisbursment_.Models;
using System.Data.SqlClient;

namespace _Cashdisbursment_.Services
{
    public class ApplicationService
    {
        private readonly DatabaseService _dbService;

        public ApplicationService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<List<Application>> GetApplicationsByCompanyAsync(int companyId)
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.CompanyID = @CompanyID
                ORDER BY a.DateRequested DESC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@CompanyID", companyId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<List<Application>> GetAllApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                ORDER BY a.DateRequested DESC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<Application?> GetApplicationByIdAsync(int applicationId)
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.ApplicationID = @ApplicationID";

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@ApplicationID", applicationId);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return MapApplicationFromReader(reader);
            }

            return null;
        }

        public async Task<int> CreateApplicationAsync(Application application)
        {
            const string query = @"
                INSERT INTO Applications (CompanyID, Description, Purpose, RequestedCash, 
                                        IsDisbursed, DateRequested, Status, ApprovalLevel, PDFLocation)
                OUTPUT INSERTED.ApplicationID
                VALUES (@CompanyID, @Description, @Purpose, @RequestedCash, 
                        @IsDisbursed, @DateRequested, @Status, @ApprovalLevel, @PDFLocation)";

            var parameters = new[]
            {
                new SqlParameter("@CompanyID", application.CompanyID),
                new SqlParameter("@Description", application.Description),
                new SqlParameter("@Purpose", application.Purpose),
                new SqlParameter("@RequestedCash", application.RequestedCash),
                new SqlParameter("@IsDisbursed", application.IsDisbursed),
                new SqlParameter("@DateRequested", application.DateRequested),
                new SqlParameter("@Status", application.Status),
                new SqlParameter("@ApprovalLevel", application.ApprovalLevel),
                new SqlParameter("@PDFLocation", (object?)application.PDFLocation ?? DBNull.Value)
            };

            var applicationId = await _dbService.ExecuteScalarAsync<int>(query, parameters);
            return applicationId;
        }

        public async Task<bool> UpdateApplicationApprovalAsync(int applicationId, int approvalLevel, 
            bool status, string? comment = null)
        {
            const string query = @"
                UPDATE Applications 
                SET ApprovalLevel = @ApprovalLevel, Status = @Status, ApprovalComment = @Comment
                WHERE ApplicationID = @ApplicationID";

            var parameters = new[]
            {
                new SqlParameter("@ApplicationID", applicationId),
                new SqlParameter("@ApprovalLevel", approvalLevel),
                new SqlParameter("@Status", status),
                new SqlParameter("@Comment", (object?)comment ?? DBNull.Value)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> DisburseApplicationAsync(int applicationId, string disbursedBy)
        {
            const string query = @"
                UPDATE Applications 
                SET IsDisbursed = 1, DisbursedBy = @DisbursedBy, DateDisbursed = @DateDisbursed
                WHERE ApplicationID = @ApplicationID AND Status = 1";

            var parameters = new[]
            {
                new SqlParameter("@ApplicationID", applicationId),
                new SqlParameter("@DisbursedBy", disbursedBy),
                new SqlParameter("@DateDisbursed", DateTime.Now)
            };

            var result = await _dbService.ExecuteNonQueryAsync(query, parameters);
            return result > 0;
        }

        public async Task<List<Application>> GetPendingApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.Status = 0
                ORDER BY a.DateRequested ASC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        public async Task<List<Application>> GetApprovedNotDisbursedApplicationsAsync()
        {
            const string query = @"
                SELECT a.ApplicationID, a.CompanyID, a.Description, a.Purpose, 
                       a.RequestedCash, a.IsDisbursed, a.DisbursedBy, a.DateRequested, 
                       a.DateDisbursed, a.Status, a.ApprovalLevel, a.ApprovalComment, 
                       a.PDFLocation, c.Name as CompanyName
                FROM Applications a
                INNER JOIN Company c ON a.CompanyID = c.CompanyID
                WHERE a.Status = 1 AND a.IsDisbursed = 0
                ORDER BY a.DateRequested ASC";

            var applications = new List<Application>();

            using var connection = _dbService.GetConnection();
            using var command = new SqlCommand(query, connection);

            await connection.OpenAsync();
            using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                applications.Add(MapApplicationFromReader(reader));
            }

            return applications;
        }

        private Application MapApplicationFromReader(SqlDataReader reader)
        {
            return new Application
            {
                ApplicationID = reader.GetInt32("ApplicationID"),
                CompanyID = reader.GetInt32("CompanyID"),
                Description = reader.GetString("Description"),
                Purpose = reader.GetString("Purpose"),
                RequestedCash = reader.GetDouble("RequestedCash"),
                IsDisbursed = reader.GetBoolean("IsDisbursed"),
                DisbursedBy = reader.IsDBNull("DisbursedBy") ? null : reader.GetString("DisbursedBy"),
                DateRequested = reader.GetDateTime("DateRequested"),
                DateDisbursed = reader.IsDBNull("DateDisbursed") ? null : reader.GetDateTime("DateDisbursed"),
                Status = reader.GetBoolean("Status"),
                ApprovalLevel = reader.GetInt32("ApprovalLevel"),
                ApprovalComment = reader.IsDBNull("ApprovalComment") ? null : reader.GetString("ApprovalComment"),
                PDFLocation = reader.IsDBNull("PDFLocation") ? null : reader.GetString("PDFLocation"),
                Company = new Company
                {
                    CompanyID = reader.GetInt32("CompanyID"),
                    Name = reader.GetString("CompanyName")
                }
            };
        }
    }
}
