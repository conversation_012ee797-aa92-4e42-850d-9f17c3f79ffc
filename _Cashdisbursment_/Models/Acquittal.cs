using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class Acquittal
    {
        public int AcquittalID { get; set; }
        
        public int ApplicationID { get; set; }
        
        // Navigation properties
        public Application? Application { get; set; }
        public List<AcquittalSubmission> AcquittalSubmissions { get; set; } = new List<AcquittalSubmission>();
    }
}
