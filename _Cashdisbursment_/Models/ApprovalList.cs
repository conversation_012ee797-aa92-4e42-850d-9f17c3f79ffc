using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class ApprovalList
    {
        public int Id { get; set; }
        
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public int ApproverNum { get; set; } // 1, 2, 3, etc. for approval hierarchy
    }
}
