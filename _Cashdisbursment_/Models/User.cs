using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class User
    {
        public int UserID { get; set; }
        
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;
        
        public bool Status { get; set; } = true;
        
        public int? CompanyID { get; set; }
        
        [StringLength(50)]
        public string Role { get; set; } = "Company"; // Company, Admin, SuperAdmin
        
        // Navigation properties
        public Company? Company { get; set; }
        public List<Log> Logs { get; set; } = new List<Log>();
    }
}
