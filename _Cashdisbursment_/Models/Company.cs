using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class Company
    {
        public int CompanyID { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;
        
        [StringLength(15)]
        public string Contact { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;
        
        public bool Status { get; set; } = true;
        
        // Navigation properties
        public List<User> Users { get; set; } = new List<User>();
        public List<Application> Applications { get; set; } = new List<Application>();
    }
}
