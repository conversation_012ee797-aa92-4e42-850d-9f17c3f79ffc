using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Models
{
    public class Log
    {
        public int LogID { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime Date { get; set; } = DateTime.Now;
        
        public int UserID { get; set; }
        
        // Navigation properties
        public User? User { get; set; }
    }
}
