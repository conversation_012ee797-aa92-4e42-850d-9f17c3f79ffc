using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;

    public IndexModel(ILogger<IndexModel> logger)
    {
        _logger = logger;
    }

    public bool IsAuthenticated { get; set; }
    public bool IsAdmin { get; set; }

    public void OnGet()
    {
        IsAuthenticated = AuthUtility.IsAuthenticated(HttpContext.Session);
        IsAdmin = AuthUtility.IsAdmin(HttpContext.Session);
    }
}
