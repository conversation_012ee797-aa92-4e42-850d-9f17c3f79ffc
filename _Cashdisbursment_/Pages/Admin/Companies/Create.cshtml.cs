using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Admin.Companies
{
    public class CreateModel : PageModel
    {
        private readonly CompanyService _companyService;
        private readonly UserService _userService;
        private readonly EmailService _emailService;

        public CreateModel(CompanyService companyService, UserService userService, EmailService emailService)
        {
            _companyService = companyService;
            _userService = userService;
            _emailService = emailService;
        }

        public User? CurrentUser { get; set; }

        [BindProperty]
        [Required]
        [StringLength(255)]
        public string CompanyName { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string CompanyEmail { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(15)]
        public string Contact { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string UserEmail { get; set; } = string.Empty;

        [BindProperty]
        public bool SendCredentials { get; set; } = true;

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Check if company email already exists
                if (await _companyService.EmailExistsAsync(CompanyEmail))
                {
                    ErrorMessage = "A company with this email already exists.";
                    return Page();
                }

                // Check if user email already exists
                var existingUser = await _userService.GetUserByEmailAsync(UserEmail);
                if (existingUser != null)
                {
                    ErrorMessage = "A user with this email already exists.";
                    return Page();
                }

                // Create company
                var company = new Company
                {
                    Name = CompanyName,
                    Email = CompanyEmail,
                    Contact = Contact,
                    Address = Address,
                    Status = true
                };

                var companyId = await _companyService.CreateCompanyAsync(company);

                if (companyId > 0)
                {
                    // Generate random password
                    var password = _userService.GenerateRandomPassword(10);

                    // Create user
                    var user = new User
                    {
                        Email = UserEmail,
                        Password = password,
                        Status = true,
                        CompanyID = companyId,
                        Role = "Company"
                    };

                    var userCreated = await _userService.CreateUserAsync(user);

                    if (userCreated)
                    {
                        // Send credentials email if requested
                        if (SendCredentials)
                        {
                            await _emailService.SendCompanyRegistrationEmailAsync(
                                CompanyEmail, CompanyName, UserEmail, password);
                        }

                        TempData["SuccessMessage"] = $"Company '{CompanyName}' created successfully!" +
                            (SendCredentials ? " Login credentials have been sent via email." : "");
                        
                        return RedirectToPage("/Admin/Companies");
                    }
                    else
                    {
                        ErrorMessage = "Company created but failed to create user account.";
                        return Page();
                    }
                }
                else
                {
                    ErrorMessage = "Failed to create company.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while creating the company. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
