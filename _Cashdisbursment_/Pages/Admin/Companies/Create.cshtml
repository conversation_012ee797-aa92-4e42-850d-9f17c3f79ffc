@page
@model _Cashdisbursment_.Pages.Admin.Companies.CreateModel
@{
    ViewData["Title"] = "Add Company";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-page="/Admin/Dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a asp-page="/Admin/Companies">Companies</a></li>
                    <li class="breadcrumb-item active">Add Company</li>
                </ol>
            </nav>
            
            <h1 class="h3 mb-4">
                <i class="fas fa-plus me-2"></i>Add New Company
            </h1>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Company Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CompanyName" class="form-label">
                                    <i class="fas fa-building me-1"></i>Company Name *
                                </label>
                                <input asp-for="CompanyName" class="form-control" placeholder="Enter company name" required />
                                <span asp-validation-for="CompanyName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="CompanyEmail" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Company Email *
                                </label>
                                <input asp-for="CompanyEmail" class="form-control" placeholder="<EMAIL>" required />
                                <span asp-validation-for="CompanyEmail" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Contact" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Contact Number
                                </label>
                                <input asp-for="Contact" class="form-control" placeholder="Phone number" />
                                <span asp-validation-for="Contact" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="UserEmail" class="form-label">
                                    <i class="fas fa-user me-1"></i>User Email *
                                </label>
                                <input asp-for="UserEmail" class="form-control" placeholder="<EMAIL>" required />
                                <span asp-validation-for="UserEmail" class="text-danger"></span>
                                <div class="form-text">This will be the login email for the company user.</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>Address
                            </label>
                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="Company address"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input asp-for="SendCredentials" class="form-check-input" checked />
                                <label asp-for="SendCredentials" class="form-check-label">
                                    Send login credentials via email
                                </label>
                                <div class="form-text">A random password will be generated and sent to the company email.</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-page="/Admin/Companies" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Create Company
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Information</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>Company Creation</h6>
                        <ul class="mb-0 small">
                            <li>Company will be created with Active status</li>
                            <li>A user account will be automatically created</li>
                            <li>Random password will be generated if email is enabled</li>
                            <li>Login credentials will be sent via email</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-shield-alt me-1"></i>Security</h6>
                        <p class="mb-0 small">The company user will be prompted to change their password on first login.</p>
                    </div>

                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-envelope me-1"></i>Email Requirements</h6>
                        <ul class="mb-0 small">
                            <li>Company email must be unique</li>
                            <li>User email must be unique</li>
                            <li>Valid email format required</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
