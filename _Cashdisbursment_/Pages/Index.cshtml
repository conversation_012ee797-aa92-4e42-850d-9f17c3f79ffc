﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home";
}

<div class="hero-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Zimdef Cash Disbursement System</h1>
                <p class="lead mb-4">Streamline your fund application and disbursement process with our comprehensive management system.</p>
                <div class="d-flex gap-3">
                    @if (Model.IsAuthenticated)
                    {
                        @if (Model.IsAdmin)
                        {
                            <a asp-page="/Admin/Dashboard" class="btn btn-light btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                        }
                        else
                        {
                            <a asp-page="/Company/Dashboard" class="btn btn-light btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>Company Dashboard
                            </a>
                        }
                    }
                    else
                    {
                        <a asp-page="/Account/Login" class="btn btn-light btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </a>
                        <a asp-page="/Account/Register" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register Company
                        </a>
                    }
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-university fa-10x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="h3 mb-3">How It Works</h2>
            <p class="text-muted">Simple and efficient fund management process</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="mb-3">
                        <i class="fas fa-user-plus fa-3x text-primary"></i>
                    </div>
                    <h5>1. Register</h5>
                    <p class="text-muted">Companies register and get verified by administrators</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="mb-3">
                        <i class="fas fa-file-alt fa-3x text-warning"></i>
                    </div>
                    <h5>2. Apply</h5>
                    <p class="text-muted">Submit fund applications with detailed documentation</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 text-center border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="mb-3">
                        <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                    </div>
                    <h5>3. Receive</h5>
                    <p class="text-muted">Get funds disbursed after approval process completion</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-12 text-center mb-5">
            <h2 class="h3 mb-3">Key Features</h2>
            <p class="text-muted">Everything you need for efficient fund management</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                </div>
                <div>
                    <h5>Multi-level Approval</h5>
                    <p class="text-muted">Configurable approval workflow with multiple levels of authorization</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-envelope fa-2x text-primary me-3"></i>
                </div>
                <div>
                    <h5>Email Notifications</h5>
                    <p class="text-muted">Automated email updates for all application status changes</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                </div>
                <div>
                    <h5>Document Management</h5>
                    <p class="text-muted">Upload and manage supporting documents for applications</p>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-chart-bar fa-2x text-info me-3"></i>
                </div>
                <div>
                    <h5>Real-time Tracking</h5>
                    <p class="text-muted">Track application status and disbursement progress in real-time</p>
                </div>
            </div>
        </div>
    </div>
</div>
