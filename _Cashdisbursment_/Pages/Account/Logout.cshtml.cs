using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Account
{
    public class LogoutModel : PageModel
    {
        public IActionResult OnGet()
        {
            AuthUtility.ClearCurrentUser(HttpContext.Session);
            return RedirectToPage("/Account/Login");
        }

        public IActionResult OnPost()
        {
            AuthUtility.ClearCurrentUser(HttpContext.Session);
            return RedirectToPage("/Account/Login");
        }
    }
}
