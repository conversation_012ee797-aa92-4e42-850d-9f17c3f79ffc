using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Account
{
    public class ChangePasswordModel : PageModel
    {
        private readonly UserService _userService;

        public ChangePasswordModel(UserService userService)
        {
            _userService = userService;
        }

        public User? CurrentUser { get; set; }

        [BindProperty]
        [Required]
        [DataType(DataType.Password)]
        public string CurrentPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(255, MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string NewPassword { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [DataType(DataType.Password)]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null)
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Verify current password
                var user = await _userService.ValidateUserAsync(CurrentUser.Email, CurrentPassword);
                if (user == null)
                {
                    ErrorMessage = "Current password is incorrect.";
                    return Page();
                }

                // Change password
                var success = await _userService.ChangePasswordAsync(CurrentUser.UserID, NewPassword);
                
                if (success)
                {
                    SuccessMessage = "Password changed successfully!";
                    return RedirectToPage();
                }
                else
                {
                    ErrorMessage = "Failed to change password. Please try again.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while changing the password. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
