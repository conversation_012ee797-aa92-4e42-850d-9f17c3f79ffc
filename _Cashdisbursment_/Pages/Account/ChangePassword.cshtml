@page
@model _Cashdisbursment_.Pages.Account.ChangePasswordModel
@{
    ViewData["Title"] = "Change Password";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>Change Password</h5>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form method="post">
                        <div class="mb-3">
                            <label asp-for="CurrentPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>Current Password
                            </label>
                            <input asp-for="CurrentPassword" class="form-control" placeholder="Enter current password" required />
                            <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="NewPassword" class="form-label">
                                <i class="fas fa-key me-1"></i>New Password
                            </label>
                            <input asp-for="NewPassword" class="form-control" placeholder="Enter new password" required />
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>

                        <div class="mb-4">
                            <label asp-for="ConfirmPassword" class="form-label">
                                <i class="fas fa-key me-1"></i>Confirm New Password
                            </label>
                            <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm new password" required />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Password Security Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>Use a combination of uppercase and lowercase letters</li>
                        <li>Include numbers and special characters</li>
                        <li>Make it at least 8 characters long</li>
                        <li>Don't use personal information</li>
                        <li>Don't reuse old passwords</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
