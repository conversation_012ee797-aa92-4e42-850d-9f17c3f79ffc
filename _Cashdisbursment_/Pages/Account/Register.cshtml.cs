using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Account
{
    public class RegisterModel : PageModel
    {
        private readonly UserService _userService;
        private readonly CompanyService _companyService;
        private readonly EmailService _emailService;

        public RegisterModel(UserService userService, CompanyService companyService, EmailService emailService)
        {
            _userService = userService;
            _companyService = companyService;
            _emailService = emailService;
        }

        [BindProperty]
        [Required]
        [StringLength(255)]
        public string CompanyName { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string CompanyEmail { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(15)]
        public string Contact { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string UserEmail { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [StringLength(255, MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [TempData]
        public string? ErrorMessage { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        public IActionResult OnGet()
        {
            // Redirect if already authenticated
            if (AuthUtility.IsAuthenticated(HttpContext.Session))
            {
                var user = AuthUtility.GetCurrentUser(HttpContext.Session);
                if (user != null)
                {
                    if (AuthUtility.IsAdmin(HttpContext.Session))
                        return RedirectToPage("/Admin/Dashboard");
                    else
                        return RedirectToPage("/Company/Dashboard");
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Check if company email already exists
                if (await _companyService.EmailExistsAsync(CompanyEmail))
                {
                    ErrorMessage = "A company with this email already exists.";
                    return Page();
                }

                // Check if user email already exists
                var existingUser = await _userService.GetUserByEmailAsync(UserEmail);
                if (existingUser != null)
                {
                    ErrorMessage = "A user with this email already exists.";
                    return Page();
                }

                // Create company
                var company = new Models.Company
                {
                    Name = CompanyName,
                    Email = CompanyEmail,
                    Contact = Contact,
                    Address = Address,
                    Status = true
                };

                var companyId = await _companyService.CreateCompanyAsync(company);

                if (companyId > 0)
                {
                    // Create user
                    var user = new User
                    {
                        Email = UserEmail,
                        Password = Password,
                        Status = true,
                        CompanyID = companyId,
                        Role = "Company"
                    };

                    var userCreated = await _userService.CreateUserAsync(user);

                    if (userCreated)
                    {
                        // Send welcome email
                        await _emailService.SendCompanyRegistrationEmailAsync(
                            CompanyEmail, CompanyName, UserEmail, Password);

                        SuccessMessage = "Registration successful! Please check your email for login credentials.";
                        return RedirectToPage("/Account/Login");
                    }
                    else
                    {
                        ErrorMessage = "Failed to create user account.";
                        return Page();
                    }
                }
                else
                {
                    ErrorMessage = "Failed to register company.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred during registration. Please try again.";
                // Log the exception in a real application
                return Page();
            }
        }
    }
}
