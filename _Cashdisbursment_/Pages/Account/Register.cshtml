@page
@model _Cashdisbursment_.Pages.Account.RegisterModel
@{
    ViewData["Title"] = "Register";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Register - Zimdef Cash Disbursement System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
</head>
<body>
    <div class="auth-container">
        <div class="auth-card fade-in" style="max-width: 500px;">
            <div class="text-center mb-4">
                <i class="fas fa-university fa-3x text-primary mb-3"></i>
                <h2>Company Registration</h2>
                <p class="text-muted">Register your company for fund applications</p>
            </div>

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
                </div>
            }

            <form method="post">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="CompanyName" class="form-label">
                            <i class="fas fa-building me-1"></i>Company Name
                        </label>
                        <input asp-for="CompanyName" class="form-control" placeholder="Enter company name" required />
                        <span asp-validation-for="CompanyName" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="CompanyEmail" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Company Email
                        </label>
                        <input asp-for="CompanyEmail" class="form-control" placeholder="<EMAIL>" required />
                        <span asp-validation-for="CompanyEmail" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Contact" class="form-label">
                            <i class="fas fa-phone me-1"></i>Contact Number
                        </label>
                        <input asp-for="Contact" class="form-control" placeholder="Phone number" />
                        <span asp-validation-for="Contact" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="UserEmail" class="form-label">
                            <i class="fas fa-user me-1"></i>User Email
                        </label>
                        <input asp-for="UserEmail" class="form-control" placeholder="<EMAIL>" required />
                        <span asp-validation-for="UserEmail" class="text-danger"></span>
                    </div>
                </div>

                <div class="mb-3">
                    <label asp-for="Address" class="form-label">
                        <i class="fas fa-map-marker-alt me-1"></i>Address
                    </label>
                    <textarea asp-for="Address" class="form-control" rows="3" placeholder="Company address"></textarea>
                    <span asp-validation-for="Address" class="text-danger"></span>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="Enter password" required />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="ConfirmPassword" class="form-label">
                            <i class="fas fa-lock me-1"></i>Confirm Password
                        </label>
                        <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm password" required />
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-user-plus me-1"></i>Register Company
                </button>
            </form>

            <div class="text-center">
                <p class="mb-0">Already have an account? 
                    <a asp-page="/Account/Login" class="text-decoration-none">Sign in here</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @section Scripts {
        @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    }
</body>
</html>
