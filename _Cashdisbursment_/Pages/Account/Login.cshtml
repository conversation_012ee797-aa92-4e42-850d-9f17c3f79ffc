@page
@model _Cashdisbursment_.Pages.Account.LoginModel
@{
    ViewData["Title"] = "Login";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Zimdef Cash Disbursement System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
</head>
<body>
    <div class="auth-container">
        <div class="auth-card fade-in">
            <div class="text-center mb-4">
                <i class="fas fa-university fa-3x text-primary mb-3"></i>
                <h2>Zimdef Cash Disbursement</h2>
                <p class="text-muted">Sign in to your account</p>
            </div>

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
                </div>
            }

            <form method="post">
                <div class="mb-3">
                    <label asp-for="Email" class="form-label">
                        <i class="fas fa-envelope me-1"></i>Email Address
                    </label>
                    <input asp-for="Email" class="form-control" placeholder="Enter your email" required />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Password" class="form-label">
                        <i class="fas fa-lock me-1"></i>Password
                    </label>
                    <input asp-for="Password" class="form-control" placeholder="Enter your password" required />
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <div class="mb-3 form-check">
                    <input asp-for="RememberMe" class="form-check-input" />
                    <label asp-for="RememberMe" class="form-check-label">
                        Remember me
                    </label>
                </div>

                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-sign-in-alt me-1"></i>Sign In
                </button>
            </form>

            <div class="text-center">
                <p class="mb-0">Don't have an account? 
                    <a asp-page="/Account/Register" class="text-decoration-none">Register here</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @section Scripts {
        @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    }
</body>
</html>
