﻿@using _Cashdisbursment_.Utilities
@{
    var currentUser = AuthUtility.GetCurrentUser(Context.Session);
    var isAuthenticated = currentUser != null;
    var isAdmin = AuthUtility.IsAdmin(Context.Session);
    var isCompany = AuthUtility.IsCompanyUser(Context.Session);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Zimdef Cash Disbursement System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-page="/Index">
                    <i class="fas fa-university me-2"></i>Zimdef Cash Disbursement
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        @if (isAuthenticated)
                        {
                            @if (isAdmin)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Dashboard"><i class="fas fa-tachometer-alt me-1"></i>Dashboard</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Companies"><i class="fas fa-building me-1"></i>Companies</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Applications"><i class="fas fa-file-alt me-1"></i>Applications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Approvers"><i class="fas fa-users me-1"></i>Approvers</a>
                                </li>
                            }
                            else if (isCompany)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Dashboard"><i class="fas fa-tachometer-alt me-1"></i>Dashboard</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Applications"><i class="fas fa-file-alt me-1"></i>My Applications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Profile"><i class="fas fa-user me-1"></i>Profile</a>
                                </li>
                            }
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (isAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-1"></i>@currentUser.Email
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-page="/Account/ChangePassword"><i class="fas fa-key me-1"></i>Change Password</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-page="/Account/Logout"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-page="/Account/Login"><i class="fas fa-sign-in-alt me-1"></i>Login</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-page="/Account/Register"><i class="fas fa-user-plus me-1"></i>Register</a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main role="main" class="flex-grow-1">
        @RenderBody()
    </main>

    <footer class="footer mt-auto">
        <div class="container">
            <p>&copy; 2025 Zimdef Cash Disbursement System. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
