using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Company
{
    public class ApplicationsModel : PageModel
    {
        private readonly ApplicationService _applicationService;

        public ApplicationsModel(ApplicationService applicationService)
        {
            _applicationService = applicationService;
        }

        public User? CurrentUser { get; set; }
        public List<Application> Applications { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? DateRange { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (CurrentUser.CompanyID.HasValue)
            {
                var allApplications = await _applicationService.GetApplicationsByCompanyAsync(CurrentUser.CompanyID.Value);
                
                // Apply filters
                Applications = FilterApplications(allApplications);
            }

            return Page();
        }

        private List<Application> FilterApplications(List<Application> applications)
        {
            var filtered = applications.AsEnumerable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                filtered = filtered.Where(a => 
                    a.Description.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    a.Purpose.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase));
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = StatusFilter.ToLower() switch
                {
                    "pending" => filtered.Where(a => !a.Status),
                    "approved" => filtered.Where(a => a.Status && !a.IsDisbursed),
                    "disbursed" => filtered.Where(a => a.IsDisbursed),
                    _ => filtered
                };
            }

            // Date range filter
            if (!string.IsNullOrEmpty(DateRange) && int.TryParse(DateRange, out int days))
            {
                var cutoffDate = DateTime.Now.AddDays(-days);
                filtered = filtered.Where(a => a.DateRequested >= cutoffDate);
            }

            return filtered.OrderByDescending(a => a.DateRequested).ToList();
        }
    }
}
