@page
@model _Cashdisbursment_.Pages.Company.ApplicationsModel
@{
    ViewData["Title"] = "My Applications";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-alt me-2"></i>My Applications
                </h1>
                <a asp-page="/Company/Applications/Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Application
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search</label>
                            <input type="text" name="search" value="@Model.SearchTerm" class="form-control" 
                                   placeholder="Search by description or purpose...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="pending" selected="@(Model.StatusFilter == "pending")">Pending</option>
                                <option value="approved" selected="@(Model.StatusFilter == "approved")">Approved</option>
                                <option value="disbursed" selected="@(Model.StatusFilter == "disbursed")">Disbursed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date Range</label>
                            <select name="dateRange" class="form-select">
                                <option value="">All Time</option>
                                <option value="7" selected="@(Model.DateRange == "7")">Last 7 days</option>
                                <option value="30" selected="@(Model.DateRange == "30")">Last 30 days</option>
                                <option value="90" selected="@(Model.DateRange == "90")">Last 90 days</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Applications 
                        <span class="badge bg-secondary">@Model.Applications.Count</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Applications.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Description</th>
                                        <th>Purpose</th>
                                        <th>Amount</th>
                                        <th>Date Requested</th>
                                        <th>Status</th>
                                        <th>Approval Level</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model.Applications)
                                    {
                                        <tr>
                                            <td>
                                                <strong>#@app.ApplicationID</strong>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                    @app.Description
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 150px;" title="@app.Purpose">
                                                    @app.Purpose
                                                </div>
                                            </td>
                                            <td>
                                                <strong>$@app.RequestedCash.ToString("N2")</strong>
                                            </td>
                                            <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (app.IsDisbursed)
                                                {
                                                    <span class="status-disbursed">
                                                        <i class="fas fa-money-bill-wave me-1"></i>Disbursed
                                                    </span>
                                                    @if (app.DateDisbursed.HasValue)
                                                    {
                                                        <small class="d-block text-muted">@app.DateDisbursed.Value.ToString("MMM dd, yyyy")</small>
                                                    }
                                                }
                                                else if (app.Status)
                                                {
                                                    <span class="status-approved">
                                                        <i class="fas fa-check-circle me-1"></i>Approved
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="status-pending">
                                                        <i class="fas fa-clock me-1"></i>Pending
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (app.ApprovalLevel > 0)
                                                {
                                                    <span class="badge bg-info">Level @app.ApprovalLevel</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Not Started</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Company/Applications/Details" asp-route-id="@app.ApplicationID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (app.IsDisbursed)
                                                    {
                                                        <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@app.ApplicationID" 
                                                           class="btn btn-sm btn-outline-success" title="Submit Acquittal">
                                                            <i class="fas fa-receipt"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">You haven't submitted any applications yet.</p>
                            <a asp-page="/Company/Applications/Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Application
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
