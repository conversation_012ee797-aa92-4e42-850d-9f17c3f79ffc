@page
@model _Cashdisbursment_.Pages.Company.ProfileModel
@{
    ViewData["Title"] = "Company Profile";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-user me-2"></i>Company Profile
            </h1>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Company Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">
                                    <i class="fas fa-building me-1"></i>Company Name *
                                </label>
                                <input asp-for="Name" class="form-control" required />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Company Email *
                                </label>
                                <input asp-for="Email" class="form-control" required />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Contact" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Contact Number
                                </label>
                                <input asp-for="Contact" class="form-control" />
                                <span asp-validation-for="Contact" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Status
                                </label>
                                <input type="text" class="form-control" 
                                       value="@(Model.Company?.Status == true ? "Active" : "Inactive")" readonly />
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>Address
                            </label>
                            <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Profile Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Company ID</label>
                        <div>#@Model.Company?.CompanyID</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Registration Date</label>
                        <div>@DateTime.Now.ToString("MMMM dd, yyyy")</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Account Status</label>
                        <div>
                            @if (Model.Company?.Status == true)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">Inactive</span>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-key me-2"></i>Account Security</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Keep your account secure by regularly updating your password.</p>
                    <a asp-page="/Account/ChangePassword" class="btn btn-outline-primary w-100">
                        <i class="fas fa-key me-1"></i>Change Password
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 text-primary">@Model.TotalApplications</div>
                                <small class="text-muted">Applications</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success">@Model.ApprovedApplications</div>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
