@page "{id:int}"
@model _Cashdisbursment_.Pages.Company.Applications.DetailsModel
@{
    ViewData["Title"] = $"Application #{Model.Application?.ApplicationID}";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-page="/Company/Dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a asp-page="/Company/Applications">Applications</a></li>
                    <li class="breadcrumb-item active">Application #@Model.Application?.ApplicationID</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-alt me-2"></i>Application #@Model.Application?.ApplicationID
                </h1>
                <div>
                    @if (Model.Application?.IsDisbursed == true)
                    {
                        <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@Model.Application.ApplicationID" 
                           class="btn btn-success">
                            <i class="fas fa-receipt me-1"></i>Submit Acquittal
                        </a>
                    }
                    <a asp-page="/Company/Applications" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Applications
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (Model.Application == null)
    {
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>Application not found or you don't have permission to view it.
        </div>
        return;
    }

    <div class="row">
        <div class="col-lg-8">
            <!-- Application Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Application Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Requested Amount</label>
                            <div class="h4 text-primary">$@Model.Application.RequestedCash.ToString("N2")</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Date Requested</label>
                            <div>@Model.Application.DateRequested.ToString("MMMM dd, yyyy 'at' hh:mm tt")</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Description</label>
                        <div class="border rounded p-3 bg-light">
                            @Model.Application.Description
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Purpose</label>
                        <div class="border rounded p-3 bg-light">
                            @Model.Application.Purpose
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Application.PDFLocation))
                    {
                        <div class="mb-3">
                            <label class="form-label fw-bold">Supporting Document</label>
                            <div>
                                <a href="@Model.Application.PDFLocation" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-file-pdf me-1"></i>View Document
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Approval Comments -->
            @if (!string.IsNullOrEmpty(Model.Application.ApprovalComment))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Approval Comments</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-comment-alt me-2"></i>@Model.Application.ApprovalComment
                        </div>
                    </div>
                </div>
            }

            <!-- Disbursement Information -->
            @if (Model.Application.IsDisbursed)
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Disbursement Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Disbursed By</label>
                                <div>@Model.Application.DisbursedBy</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Disbursed</label>
                                <div>@Model.Application.DateDisbursed?.ToString("MMMM dd, yyyy 'at' hh:mm tt")</div>
                            </div>
                        </div>
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>
                            Funds have been successfully disbursed. Please submit your acquittal documentation as required.
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Application Status</h6>
                </div>
                <div class="card-body text-center">
                    @if (Model.Application.IsDisbursed)
                    {
                        <div class="mb-3">
                            <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                        </div>
                        <h5 class="text-success">Disbursed</h5>
                        <p class="text-muted">Funds have been disbursed</p>
                    }
                    else if (Model.Application.Status)
                    {
                        <div class="mb-3">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                        </div>
                        <h5 class="text-success">Approved</h5>
                        <p class="text-muted">Awaiting disbursement</p>
                    }
                    else
                    {
                        <div class="mb-3">
                            <i class="fas fa-clock fa-3x text-warning"></i>
                        </div>
                        <h5 class="text-warning">Pending</h5>
                        <p class="text-muted">Under review</p>
                    }
                </div>
            </div>

            <!-- Approval Progress -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Approval Progress</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Approval Level</span>
                            <span class="badge bg-info">@Model.Application.ApprovalLevel / @Model.MaxApprovalLevel</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: @(Model.MaxApprovalLevel > 0 ? (Model.Application.ApprovalLevel * 100.0 / Model.MaxApprovalLevel) : 0)%">
                            </div>
                        </div>
                    </div>
                    
                    @if (Model.Application.ApprovalLevel == 0)
                    {
                        <small class="text-muted">Approval process not yet started</small>
                    }
                    else if (Model.Application.Status)
                    {
                        <small class="text-success">All approvals completed</small>
                    }
                    else
                    {
                        <small class="text-info">Currently at approval level @Model.Application.ApprovalLevel</small>
                    }
                </div>
            </div>

            <!-- Application Summary -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 text-primary">$@Model.Application.RequestedCash.ToString("N0")</div>
                                <small class="text-muted">Requested</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-info">@((DateTime.Now - Model.Application.DateRequested).Days)</div>
                            <small class="text-muted">Days Ago</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
