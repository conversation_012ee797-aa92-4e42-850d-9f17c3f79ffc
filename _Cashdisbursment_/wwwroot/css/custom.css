/* Custom CSS for Zimdef Cash Disbursement System */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --dark-text: #2c3e50;
    --border-color: #dee2e6;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    color: var(--dark-text);
}

/* Header Styles */
.navbar-brand {
    font-weight: 600;
    color: var(--primary-color) !important;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background-color: white !important;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    margin-bottom: 20px;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px 8px 0 0 !important;
    font-weight: 500;
}

/* But<PERSON> Styles */
.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 20px;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Form Styles */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    padding: 10px 15px;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-text);
    margin-bottom: 5px;
}

/* Table Styles */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 500;
    padding: 15px;
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status Badges */
.status-pending {
    background-color: var(--warning-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-approved {
    background-color: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-rejected {
    background-color: var(--danger-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-disbursed {
    background-color: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, var(--secondary-color), #5dade2);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.dashboard-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Login/Register Forms */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.auth-card h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
    font-weight: 600;
}

/* Sidebar Navigation */
.sidebar {
    background-color: var(--primary-color);
    min-height: calc(100vh - 56px);
    padding: 20px 0;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 20px 0;
    margin-top: 50px;
}

.footer p {
    margin-bottom: 0;
    text-align: center;
}
